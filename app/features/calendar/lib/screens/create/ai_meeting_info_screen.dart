import 'package:flutter/material.dart';
import 'package:gp_core/core.dart';

class AIMeetingInfoScreen extends StatelessWidget {
  const AIMeetingInfoScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: GPColor.bgPrimary,
      body: <PERSON><PERSON><PERSON>(
        child: Column(
          children: [
            // Main content
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24),
                child: Column(
                  children: [
                    const SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(8)),
                        child: Image.asset(
                          Assets
                              .PACKAGES_GP_ASSETS_IMAGES_AI_MEETING_BANNER_PNG,
                          fit: BoxFit.contain,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Title
                    Text(
                      LocaleKeys.calendar_ai_meeting_info_page_title.tr,
                      textAlign: TextAlign.center,
                      style: textStyle(GPTypography.displaySmall)?.copyWith(
                        color: GPColor.contentPrimary,
                        height: 1.33,
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Feature list
                    Column(
                      children: [
                        FutureItem(
                            emoji: '👉',
                            boldText: LocaleKeys
                                .calendar_ai_meeting_info_page_description_bold_1
                                .tr,
                            text: LocaleKeys
                                .calendar_ai_meeting_info_page_description_1
                                .tr),
                        const SizedBox(height: 12),
                        FutureItem(
                            emoji: '👉',
                            boldText: LocaleKeys
                                .calendar_ai_meeting_info_page_description_bold_2
                                .tr,
                            text: LocaleKeys
                                .calendar_ai_meeting_info_page_description_2
                                .tr),
                      ],
                    ),

                    const SizedBox(height: 16),

                    // Contact section
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: GPColor.linePrimary),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Contact header
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                LocaleKeys
                                    .calendar_ai_meeting_info_page_contact_title
                                    .tr,
                                style: textStyle(GPTypography.headingSmall)
                                    ?.copyWith(
                                  color: GPColor.contentSecondary,
                                  fontSize: 12,
                                ),
                              ),
                              const SizedBox(height: 4),
                              InkWell(
                                onTap: () {
                                  Deeplink.onTapUrl('1900633388');
                                },
                                child: Text(
                                  '1900.633.388',
                                  style: textStyle(GPTypography.displayLarge)
                                      ?.copyWith(
                                    color: GPColor.blue,
                                  ),
                                ),
                              ),
                            ],
                          ),

                          const SizedBox(height: 16),

                          // Contact details
                          Column(
                            children: [
                              ContactItem(
                                  icon: Assets
                                      .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_MAIL_SVG,
                                  text: '<EMAIL>'),
                              const SizedBox(height: 12),
                              ContactItem(
                                  icon: Assets
                                      .PACKAGES_GP_ASSETS_IMAGES_SVG_IC20_LINE15_PHONE_SVG,
                                  text: '0247.3000.585'),
                            ],
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),

            // Bottom button
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: GPColor.bgPrimary,
                boxShadow: [
                  BoxShadow(
                    color: const Color(0x1A2F313A),
                    blurRadius: 1,
                    offset: const Offset(0, -1),
                  ),
                ],
              ),
              child: SafeArea(
                top: false,
                child: SizedBox(
                  width: double.infinity,
                  child: GPWorkButton(
                    title:
                        LocaleKeys.calendar_ai_meeting_info_page_understand.tr,
                    onTap: () {
                      Utils.back();
                    },
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class FutureItem extends StatelessWidget {
  const FutureItem({
    super.key,
    required this.emoji,
    required this.boldText,
    required this.text,
  });

  final String emoji;
  final String boldText;
  final String text;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          emoji,
          style: textStyle(GPTypography.bodyMedium),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: RichText(
              text: TextSpan(children: [
            TextSpan(
              text: boldText,
              style: textStyle(GPTypography.headingMedium)?.copyWith(
                color: GPColor.contentPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
            TextSpan(
              text: text,
              style: textStyle(GPTypography.bodyLarge)?.copyWith(
                color: GPColor.contentSecondary,
              ),
            ),
          ])),
        ),
      ],
    );
  }
}

class ContactItem extends StatelessWidget {
  const ContactItem({
    super.key,
    required this.icon,
    required this.text,
  });

  final String icon;
  final String text;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Deeplink.onTapUrl(text);
      },
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: SvgWidget(
              icon,
              width: 20,
              height: 20,
              color: GPColor.functionAccentWorkSecondary,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: textStyle(GPTypography.bodyLarge)
                  ?.copyWith(color: GPColor.contentPrimary, height: 1.2),
            ),
          ),
        ],
      ),
    );
  }
}
