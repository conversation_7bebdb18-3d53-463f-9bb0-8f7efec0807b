import 'dart:ui';

import 'package:gp_core/core.dart';
import 'package:gp_feat_task/models/tag/tag_model.dart';
import 'package:gp_feat_task/screens/task_details/upload/model/task_attachment_file.dart';

import 'task_response_extend.dart';

export 'task_response_extend.dart';

class Task {
  Task({
    required this.id,
    this.title,
    this.description,
    this.assignees,
    this.watchers,
    this.creator,
    this.status,
    this.dueDate,
    this.startDate,
    this.priority,
    this.createdAt,
    this.updatedAt,
    this.attachmentFiles,
    this.taskUrl,
    //v2
    this.workspaceId,
    this.archiveAt,
    this.creatorId,
    this.projectId,
    this.folderId,
    this.taskListId,
    this.projectName,
    this.taskListName,
    this.contentRtf,
    this.parentTaskId,
    this.subTaskIds,
    this.subTasks,
    this.sectionId,
    this.sectionName,
    this.depth,
    this.ancestorId,
    this.parentTask,
    this.listTag,
    this.openTaskFromDeepLink,
    this.rRule,
    this.recurrenceSettings,
    this.blockingIds,
    this.blockingTasks,
    this.waitingIds,
    this.waitingTasks,
    this.replyCommentId,
  });

  Task clone({bool isDuplicating = false}) {
    final taskClone = Task.fromJson(toJson());
    if (isDuplicating) {
      final copyOfString = LocaleKeys.task_copyOf.tr;
      final count = 200 - (taskClone.name.length + copyOfString.length);
      if (count >= 0) {
        taskClone.title = "$copyOfString${taskClone.name}";
      } else {
        final title = "$copyOfString${taskClone.name}".substring(0, 200);
        taskClone.title = title;
      }
      taskClone.id = "";
      taskClone.isDuplicating = true;
      taskClone.status = null;
      taskClone.dueDate = null;
      taskClone.assignees = null;
      taskClone.watchers = null;
      taskClone.priority = 0;
      taskClone.creator = GPUserModel(id: int.tryParse(Constants.userId()));
      taskClone.parentTask = null;
      taskClone.depth = null;
    }
    taskClone.subTasks = subTasks
        ?.map((e) => e
          ..status = 0
          ..id = ""
          ..parentTaskId = "")
        .toList();
    return taskClone;
  }

  String get name {
    return title ?? "";
  }

  bool get hasPriority => priority != null && priority != 0;

  String id;
  bool? isDuplicating;

  String? title;
  String? description;
  List<GPUserModel>? assignees;
  List<GPUserModel>? watchers;
  GPUserModel? creator;
  int? status;
  int? dueDate;
  int? startDate;
  int? priority;
  int? createdAt;
  int? updatedAt;
  String? taskUrl;
  bool? contentRtf;

  String? workspaceId;
  int? archiveAt;
  int? creatorId;
  String? projectId;
  String? folderId;
  String? taskListId;

  String? projectName;
  String? taskListName;

  String? parentTaskId;
  List<String>? subTaskIds;
  List<Task>? subTasks;

  List<TagModel>? listTag;
  Task? parentTask;

  // dùng tại local, nav tới commentId tương ứng
  String? commentId;
  String? replyCommentId;

  String? sectionId;
  String? sectionName;

  bool? openTaskFromDeepLink;

  bool get isArchived {
    return archiveAt != null;
  }

  bool get hasDependenceTask =>
      blockingTasks?.isNotEmpty == true || waitingTasks?.isNotEmpty == true;

  // the uploaded file repsponses and to be added to task by edit task api
  // List<UploadResponseModel>? uploadAttachmentFiles;

  // the file that already attached to the task
  List<TaskAttachmentFile>? attachmentFiles;

  //add model v2 sub task
  String? ancestorId;
  int? depth;
  List<Task>? waitingTasks;
  List<Task>? blockingTasks;
  List<dynamic>? waitingIds;
  List<dynamic>? blockingIds;

  String? rRule;

  RecurrenceSettings? recurrenceSettings;

  factory Task.fromJson(Map<String, dynamic> json) {
    final List<dynamic>? assignees = json["assignees"] != null
        ? (json["assignees"] as List).whereNotNull().toList()
        : null;
    final List<dynamic>? watchers = json["watchers"] != null
        ? (json["watchers"] as List).whereNotNull().toList()
        : null;
    return Task(
      sectionId: json["section_id"],
      sectionName: json["section_name"],
      id: json["id"],
      title: json["title"],
      contentRtf: json["content_rtf"],
      description: json['description'] != null
          ? json['description'] is Map
              ? json['description']['text']
              : json['description']
          : null,
      assignees: assignees != null
          ? List<GPUserModel>.from(assignees.map((x) {
              if (x is int) {
                return GPUserModel(id: x);
              } else {
                return GPUserModel.fromJson(x);
              }
            }))
          : null,
      watchers: watchers != null
          ? List<GPUserModel>.from(watchers.map((x) {
              if (x is int) {
                return GPUserModel(id: x);
              } else {
                return GPUserModel.fromJson(x);
              }
            }))
          : null,
      creator: json["creator"] != null
          ? GPUserModel.fromJson(json["creator"])
          : null,
      status: json["status"],
      dueDate: json["due_date"],
      startDate: json["start_date"],
      priority: json["priority"],
      createdAt: json["created_at"],
      updatedAt: json["updated_at"],
      attachmentFiles: json['attachment_files'] != null
          ? List<TaskAttachmentFile>.from(json["attachment_files"]
              .map((x) => TaskAttachmentFile.fromJson(x)))
          : null,
      taskUrl: json['task_url'],
      // v2
      workspaceId: json['workspace_id'],
      archiveAt: json['archived_at'],
      creatorId: json['creator_id'],
      waitingTasks: json['waiting_tasks'] != null
          ? List<Task>.from(json['waiting_tasks'].map((e) => Task.fromJson(e)))
          : null,
      blockingTasks: json['blocking_tasks'] != null
          ? List<Task>.from(json['blocking_tasks'].map((e) => Task.fromJson(e)))
          : null,
      blockingIds: json['blocking_ids'],
      waitingIds: json['waiting_ids'],
      projectId: json['project_id'],
      folderId: json['folder_id'],
      taskListId: json['task_list_id'],
      projectName: json['project_name'],
      taskListName: json['task_list_name'],
      parentTaskId: json['parent_task_id'],
      subTaskIds: json['subtask_ids'] != null
          ? List<String>.from(json['subtask_ids'])
          : null,
      subTasks: json['subtasks'] != null
          ? List<Task>.from(json['subtasks'].map((e) => Task.fromJson(e)))
          : null,
      depth: json['depth'] ?? 0,
      ancestorId: json['ancestor_id'] ?? '',
      parentTask: json['parent_task'] != null
          ? Task.fromJson(json['parent_task'])
          : null,
      listTag: (json['tags'] as List<dynamic>?)
              ?.map((e) => TagModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      rRule: json["rrule"],
      recurrenceSettings: json["recurrence_settings"] != null
          ? RecurrenceSettings.fromJson(json["recurrence_settings"])
          : null,
    );
  }

  factory Task.fromChatMessageJson(Map<String, dynamic> json) {
    // if (json["attachment_files"] != null) {
    //   List<TaskAttachmentFile> attachments = json["attachment_files"];
    //   for (var e in attachments) {
    //     if (e.taskAttachmentFileType == TaskAttachmentFileType.image) {
    //       e.imageId = e.fileId;
    //     } else if (e.taskAttachmentFileType == TaskAttachmentFileType.video) {
    //       e.videoId = e.fileId;
    //     }
    //   }
    // }

    final Iterable? assignees = json['assignees'];

    final Iterable? watchers = json['watchers'];

    final Iterable? attachmentFiles = json["attachment_files"];

    return Task(
      id: "",
      description: json["description"],
      creator: GPUserModel.fromJson(Map<String, dynamic>.from(json["creator"])),
      assignees: json["assignees"] != null
          ? List<GPUserModel>.from(assignees!.map((x) {
              if (x is int) {
                return GPUserModel(id: x);
              } else {
                return GPUserModel.fromJson(Map<String, dynamic>.from(x));
              }
            }))
          : null,
      watchers: json["watchers"] != null
          ? List<GPUserModel>.from(watchers!.map((x) {
              if (x is int) {
                return GPUserModel(id: x);
              } else {
                return GPUserModel.fromJson(Map<String, dynamic>.from(x));
              }
            }))
          : null,
      attachmentFiles: json['attachment_files'] != null
          ? List<TaskAttachmentFile>.from(attachmentFiles!.map(
              (x) => TaskAttachmentFile.fromJson(Map<String, dynamic>.from(x))))
          : null,
    );
  }

  /// to use to create task
  Map<String, dynamic> toCreateTaskRequestBody() {
    var body = {
      "title": title,
      "description": (description?.isNotEmpty ?? false) ? description : '',
      "assignees": assignees?.map((e) => e.id).toList() ?? [],
      "watchers": watchers?.map((e) => e.id).toList() ?? [],
      "creator": creator?.toJson(),
      "status": status ?? 0,
      "due_date": dueDate,
      "start_date": startDate,
      "priority": priority,
      "attachment_files":
          List<dynamic>.from((attachmentFiles ?? []).map((e) => e.toJson())),
      // v2
      "workspace_id": workspaceId,
      "archived_at": archiveAt,
      "creator_id": creatorId,
      "task_list_id": taskListId,
      "project_name": projectName,
      "task_list_name": taskListName,
      "parent_task_id": parentTaskId,
      "subtask_ids": subTaskIds,
      "subtasks": List<dynamic>.from(
          (subTasks ?? []).map((e) => e.toCreateChecklistNewTask())),
      "section_id": sectionId,
      "content_rtf": contentRtf,
      "ancestor_id": ancestorId,
      "depth": depth,
      "parent_task": parentTask,
      "tag_ids": listTag?.map((e) => e.id).toList() ?? [],
      "rrule": rRule,
      "recurrence_settings": recurrenceSettings,
    };

    return body;
  }

  Map<String, dynamic> toCreateChecklistNewTask() => {
        "title": title,
        "description": "subtask",
        "status": status ?? 0,
        "task_list_id": taskListId,
      };

  /// to use to edit task
  Map<String, dynamic> toEditTaskRequestBody() {
    // new attachment file that just uploaded
    // if (uploadAttachmentFiles is List) {
    //   attachmentFilesBody
    //       .addAll(uploadAttachmentFiles!.map((e) => e.toAttachmentFileJson()));
    // }

    var body = {
      "title": title,
      "description": (description?.isNotEmpty ?? false) ? description : null,
      "assignees": assignees?.map((e) => e.id).toList() ?? [],
      "watchers": watchers?.map((e) => e.id).toList() ?? [],
      "status": status,
      "due_date": dueDate,
      "priority": priority,
      "attachment_files": (attachmentFiles?.isNotEmpty ?? false)
          ? List<dynamic>.from((attachmentFiles ?? []).map((e) => e.toJson()))
          : null,
      // v2
      // "workspace_id": workspaceId,
      "archived_at": archiveAt,
      "task_list_id": taskListId,
      "parent_task_id": parentTaskId,
      "subtask_ids": subTaskIds,
      "subtasks": List<dynamic>.from((subTasks ?? []).map((e) => e.toJson())),
      "ancestor_id": ancestorId,
      "depth": depth,
      "parent_task": parentTask
    };
    return body;
  }

  Task copyWith({
    String? title,
    String? description,
    List<GPUserModel>? assignees,
    List<GPUserModel>? watchers,
    GPUserModel? creator,
    int? status,
    int? dueDate,
    int? priority,
    int? createdAt,
    int? updatedAt,
    String? taskUrl,
    List<TaskAttachmentFile>? attachmentFiles,
    String? workspaceId,
    int? archiveAt,
    int? creatorId,
    String? projectId,
    String? folderId,
    String? taskListId,
    String? projectName,
    String? taskListName,
    String? parentTaskId,
    List<String>? subTaskIds,
    List<Task>? subTasks,
    int? depth,
  }) =>
      Task(
          id: id,
          title: title ?? this.title,
          description: description ?? this.description,
          assignees: assignees ?? this.assignees,
          watchers: watchers ?? this.watchers,
          creator: creator ?? this.creator,
          status: status ?? this.status,
          dueDate: dueDate ?? this.dueDate,
          priority: priority ?? this.priority,
          createdAt: createdAt ?? this.createdAt,
          updatedAt: updatedAt ?? this.updatedAt,
          attachmentFiles: attachmentFiles ?? this.attachmentFiles,
          taskUrl: taskUrl ?? this.taskUrl,
          workspaceId: workspaceId ?? this.workspaceId,
          archiveAt: archiveAt ?? this.archiveAt,
          creatorId: creatorId ?? this.creatorId,
          projectId: projectId ?? this.projectId,
          folderId: folderId ?? this.folderId,
          taskListId: taskListId ?? this.taskListId,
          projectName: projectName ?? this.projectName,
          taskListName: taskListName ?? this.taskListName,
          parentTaskId: parentTaskId ?? this.parentTaskId,
          subTaskIds: subTaskIds ?? this.subTaskIds,
          subTasks: subTasks ?? this.subTasks,
          sectionId: sectionId,
          sectionName: sectionName,
          depth: depth);

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "description": description,
        "assignees":
            assignees?.map((e) => e.id).toList(), // for api create task
        // List<dynamic>.from((assignees ?? []).map((x) => x.toJson())),
        "watchers": watchers?.map((e) => e.id).toList(), // for api create task
        // List<dynamic>.from((watchers ?? []).map((x) => x.toJson())),
        "creator": creator?.toJson(),
        "status": status,
        "due_date": dueDate,
        "start_date": startDate,
        "priority": priority,
        "created_at": createdAt,
        "updated_at": updatedAt,
        "task_url": taskUrl,
        // v2
        "project_id": projectId,
        "folder_id": folderId,
        "task_list_id": taskListId,
        "archived_at": archiveAt,
        "content_rtf": contentRtf,
        "project_name": projectName ?? "",
        "task_list_name": taskListName,
        "attachment_files":
            List<dynamic>.from((attachmentFiles ?? []).map((e) => e.toJson())),
        "section_id": sectionId,
        "section_name": sectionName,
        "ancestor_id": ancestorId,
        "depth": depth,
        "parent_task": parentTask,
        "tags": listTag?.map((e) => e.id).toList(),
      };

  @override
  bool operator ==(other) {
    return (other is Task) && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  TaskStatus get taskStatus {
    final result = TaskStatus.values
        .firstWhere((element) => element.value == (status ?? 0));
    return result;
  }

  bool get isCompleted {
    return taskStatus == TaskStatus.done;
  }

  Color get titleColor {
    return isCompleted ? GPColor.contentSecondary : GPColor.contentPrimary;
  }

  bool get hasSubTask => subTasks?.isNotEmpty ?? false;

  bool get showLessAssigneeOnList {
    return dueDate != 0 && hasPriority && hasSubTask;
  }

  bool get doneTaskDependency {
    if ((waitingTasks ?? []).isEmpty) {
      return true;
    }

    int index = (waitingTasks ?? [])
        .indexWhere((element) => element.taskStatus == TaskStatus.todo);

    if (index != -1) {
      return false;
    }

    return true;
  }
}

class GPUserModel {
  GPUserModel({
    required this.id,
    this.displayName,
    this.avatar,
    this.avatarThumbPattern,
  });

  int? id;
  String? displayName;
  String? avatar;
  String? avatarThumbPattern;

  factory GPUserModel.fromJson(Map<String, dynamic> json) => GPUserModel(
        id: json["id"] is String ? int.tryParse(json["id"]) : json["id"],
        displayName: json["display_name"],
        avatar: json["avatar"],
        avatarThumbPattern: json["avatar_thumb_pattern"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "display_name": displayName,
        "avatar": avatar,
        "avatar_thumb_pattern": avatarThumbPattern,
      };

  @override
  bool operator ==(other) {
    return (other is GPUserModel) && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

class RecurrenceSettings {
  RecurrenceSettings({
    this.assignee,
    this.subtask,
    this.watcher,
    this.dueDateTime,
  });

  bool? subtask;
  bool? assignee;
  bool? watcher;
  String? dueDateTime;

  factory RecurrenceSettings.fromJson(Map<String, dynamic> json) =>
      RecurrenceSettings(
        assignee: json["assignee"] ?? false,
        subtask: json["subtask"] ?? false,
        watcher: json["watcher"] ?? false,
        dueDateTime: json["due_date_time"],
      );

  Map<String, dynamic> toJson() => {
        "assignee": assignee,
        "subtask": subtask,
        "watcher": watcher,
        "due_date_time": dueDateTime,
      };

  DateTime? get dueDateDateTime =>
      DateTimeUtils.instance.convert24hStringTimeToDateTimeToday(dueDateTime);
}

// class Links {
//   Links({
//     required this.prev,
//     required this.next,
//   });

//   String prev;
//   String next;

//   factory Links.fromJson(Map<String, dynamic> json) => Links(
//         prev: json["prev"],
//         next: json["next"],
//       );

//   Map<String, dynamic> toJson() => {
//         "prev": prev,
//         "next": next,
//       };
// }
