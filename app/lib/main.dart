/*
 * Created Date: 4/01/2024 13:51:20
 * Author: <PERSON><PERSON><PERSON>
 * -----
 * Last Modified: Friday, 19th September 2025 11:03:47
 * Modified By: toannm
 * -----
 * Copyright (c) 2021 - 2025 GAPO
 */

// ignore_for_file: depend_on_referenced_packages

import 'dart:async';
import 'dart:io';

import 'package:device_preview/device_preview.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:gapo_flutter_app/utils/route_to_interaction.dart';
import 'package:get_it/get_it.dart';
import 'package:gp_core/base/networking/base/feature_flag.dart';
import 'package:gp_core/base/networking/services/auth_api.dart';
import 'package:gp_core/core.dart';
import 'package:gp_core/navigator/router_mapping.dart';
import 'package:gp_core/navigator/tag_management.dart';
import 'package:gp_core/routes/pages.dart';
import 'package:gp_core/utils/gp_sentry.dart';
import 'package:gp_core_v2/base/constants/constants.dart';
import 'package:gp_dio_log/gp_dio_log.dart';
import 'package:gp_feat_ai_market/route/routes.dart';
import 'package:gp_feat_ca/route/route.dart';
import 'package:gp_feat_calendar/feat_calendar.dart';
import 'package:gp_feat_calendar/routes/pages.dart';
import 'package:gp_feat_calendar/routes/router_name.dart';
import 'package:gp_feat_coin/route/route.dart';
import 'package:gp_feat_drive/route/route.dart';
import 'package:gp_feat_member_picker/routes/pages.dart';
import 'package:gp_feat_portal/route/pages.dart';
import 'package:gp_feat_portal/route/router_name.dart';
import 'package:gp_feat_task/routes/pages.dart';
import 'package:gp_feat_task/routes/router_name.dart';
import 'package:gp_feat_ticket/route/route.dart';
import 'package:gp_feat_time_keeping/routes/pages.dart';
import 'package:gp_feat_time_keeping/routes/router_name.dart';
import 'package:gp_feat_project_management/route/pages.dart';
import 'package:gp_feat_project_management/route/router_name.dart';
import 'package:gp_native_shared/rtf_editor/routes/pages.dart';
import 'package:gp_native_shared/rtf_editor/routes/router_name.dart';
import 'package:sentry_logging/sentry_logging.dart';
import 'package:talker/talker.dart';
import 'package:talker_bloc_logger/talker_bloc_logger_observer.dart';
import 'package:timezone/data/latest.dart' as tz;

import 'di/component/app.component.dart' as app;
import 'pages/pages.dart';
import 'screen_tracking/screen_tracking.dart';
import 'utils/router_mapping.dart';
import 'utils/tablet_detector.dart';

const _maxScaleFactor = 1.0;

bool isInitV2 = false;

const appLocales = [
  Locale('vi'),
  Locale('en'),
  // Locale('id'),
  // Locale('th'),
];

Future<void> _initApp({bool fromNative = false}) async {
  FeatureFlag.isFromNative = fromNative;

  Get.lazyPut(() => TagController());

  WidgetsFlutterBinding.ensureInitialized();
  await GetStorage.init();
  if (!fromNative) {
    await TokenManager.initToken(
      email: "<EMAIL>", // <EMAIL>
      password: "1234abcd@", //Gapo@123
    );

    Constants.updateThemeByFlavor(isFromNative: fromNative);

    await initAppWithCoreV2();
  }

  // statusBar + system navigation
  SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  LogPoolManager.getInstance()?.maxCount = 1000;

  // if (GetPlatform.isIOS && (kProfileMode || kDebugMode)) {
  //   ShakeDetector.autoStart(onPhoneShake: () => Get.toNamed(RouterName.log));
  // }

  // Pages.pages.addAll(AlbumPages.pages);
  Pages.pages.addAll(TaskPages.pages);
  Pages.pages.addAll(CalendarPages.pages);
  Pages.pages.addAll(MemberPickerPages.pages);
  Pages.pages.addAll(TimeKeepingPages.pages);
  Pages.pages.addAll(RtfEditorPages.pages);
  Pages.pages.addAll(CAPages.pages);
  Pages.pages.addAll(TicketPages.pages);
  Pages.pages.addAll(CoinPages.pages);
  // Pages.pages.addAll(DrivePages.pages);
  Pages.pages.addAll(DrivePages.pages);
  Pages.pages.addAll(AppPages.pages);
  Pages.pages.addAll(PortalPages.pages);
  Pages.pages.addAll(ProjectManagementPages.pages);
  Pages.pages.addAll(AIMarketPages.pages);

  RouterMapping.instance.routerEntryPointMapping.addAll(routerMap);

  HttpOverrides.global = GPHttpOverrides();
}

Future<void> main() async {
  await _initApp();
  tz.initializeTimeZones();

  runApp(
    const LoadingApp(
      routeName: TicketRoutes.ticketMain, // TimeKeepingRoutes.timeKeeping
      fromNative: false,
      language: 'en',
    ),
  );
}

@pragma('vm:entry-point')
void tasksMain() {
  _runGapoApp(TaskRouterName.tasks);
}

@pragma('vm:entry-point')
void taskCollabMain() {
  _runGapoApp(TaskRouterName.taskCollab);
}

@pragma('vm:entry-point')
void taskArchiveCollabMain() {
  _runGapoApp(TaskRouterName.taskArchiveCollab);
}

@pragma('vm:entry-point')
void taskProjectMain() {
  _runGapoApp(TaskRouterName.taskProject);
}

@pragma('vm:entry-point')
void taskGeneralMain() {
  _runGapoApp(TaskRouterName.taskMain);
}

@pragma('vm:entry-point')
void projectDetailMain() {
  // hiện tại đều chuyển qua màn project list
  _runGapoApp(TaskRouterName.taskProject);
}

@pragma('vm:entry-point')
void findingColleaguesMain() {
  _runGapoApp(RouterName.selectInvitees);
}

@pragma('vm:entry-point')
void calendarNotificationsMain() {
  _runGapoApp(CalendarRouterName.calendarNotifications);
}

@pragma('vm:entry-point')
void calendarMain() {
  _runGapoApp(CalendarRouterName.calendar);
}

@pragma('vm:entry-point')
void calendarCollabMain() {
  _runGapoApp(CalendarRouterName.calendarCollab);
}

@pragma('vm:entry-point')
void calendarEventDetailMain() {
  _runGapoApp(CalendarRouterName.calendarEventDetail);
}

@pragma('vm:entry-point')
void createCalendarEventMain() {
  _runGapoApp(CalendarRouterName.createCalendarEvent);
}

@pragma('vm:entry-point')
void nativeMain() {
  _runGapoApp(TaskRouterName.taskMain);
}

@pragma('vm:entry-point')
void taskNotiMain() {
  _runGapoApp(TaskRouterName.notifications);
}

@pragma('vm:entry-point')
void taskDetailMain() {
  _runGapoApp(TaskRouterName.taskCreate);
}

// @pragma('vm:entry-point')
// void albumListMain() {
//   _runGapoApp(AlbumRouterName.album);
// }

// @pragma('vm:entry-point')
// void createAlbumMain() {
//   _runGapoApp(AlbumRouterName.createAlbum);
// }

// @pragma('vm:entry-point')
// void albumDetailMain() {
//   _runGapoApp(AlbumRouterName.albumDetail);
// }

@pragma('vm:entry-point')
void taskCollab() {
  _runGapoApp(TaskRouterName.taskCollab);
}

@pragma('vm:entry-point')
void assigneePickerMain() {
  _runGapoApp(RouterName.selectInvitees);
}

@pragma('vm:entry-point')
void rtfEditorMain() {
  _runGapoApp(RtfEditorRouterName.rtfEditor);
}

@pragma('vm:entry-point')
void timeKeepingMain() {
  _runGapoApp(TimeKeepingRoutes.timeKeeping);
  AuthAPI().updateUUId();
}

// @pragma('vm:entry-point')
// void driveMain() {
//   _runGapoApp(DriveRoutes.driveMain);
// }

@pragma('vm:entry-point')
void ticketMain() {
  _runGapoApp(TicketRoutes.ticketMain);
}

@pragma('vm:entry-point')
void coinMain() {
  _runGapoApp(CoinRoutes.coinMain);
}

@pragma('vm:entry-point')
void portalMain() {
  _runGapoApp(PortalRoutes.portalMain);
}

@pragma('vm:entry-point')
void projectManagementMain() {
  _runGapoApp(ProjectManagementRoutes.projectManagementMain);
}

@pragma('vm:entry-point')
void aiMarketMain() {
  _runGapoApp(AIMarketRoutes.aiMarketMain);
}

@pragma('vm:entry-point')
void aiMeetingBotInfoMain() {
  _runGapoApp(CalendarRouterName.aiAssistantInfo);
}

void _runGapoApp(String routeName) {
  runZonedGuarded(() async {
    await _initApp(fromNative: true);
    tz.initializeTimeZones();

    // await SentryFlutter.init((options) {
    //   _initSentry(options);
    // }, appRunner: () {
    //   runApp(LoadingApp(routeName: routeName, fromNative: true));
    // });
    if (!kDebugMode) {
      SentryFlutter.init((options) {
        _initSentry(options);
      }).catchError((e) {
        logDebug('SentryFlutter onError: $e');
      });
    }

    runApp(LoadingApp(routeName: routeName, fromNative: true));

    removeCopyFolder();
  }, (exception, stackTrace) async {
    if (GetPlatform.isAndroid) {
      Deeplink.recordError(error: exception, stackTrace: stackTrace);
    } else {
      GPCoreTracker().appendError(
        'Flutter:main.runZonedGuarded',
        data: {'error': exception, 'stacktrace': stackTrace},
      );

      GPCoreTracker().sendLog(message: 'Flutter:main.runZonedGuarded');
    }
  });
}

Future initAppWithCoreV2() async {
  if (isInitV2) return;

  await app.configureInjection(environmentFilters: _environmentFilters);

  isInitV2 = true;

  if (GetIt.I.isRegistered<Talker>()) {
    Bloc.observer = TalkerBlocObserver(talker: GetIt.instance<Talker>());
  } else {
    logDebug("Talker is not registered");
  }
}

class LoadingApp extends StatelessWidget {
  final String routeName;
  final String language;
  final bool fromNative;

  const LoadingApp({
    super.key,
    this.routeName = TaskRouterName.tasks,
    this.language = "vi",
    this.fromNative = false,
  });

  @override
  Widget build(BuildContext context) {
    Get.put<String>(routeName, tag: "ROUTE");

    if (FeatureFlag.enableDevicePreview &&
        (kProfileMode || kDebugMode) &&
        TabletDetector.isTablet()) {
      return DevicePreview(
        enabled: !kReleaseMode,
        builder: (context) => _GapoApp(
          routeName: routeName,
          language: language,
          fromNative: fromNative,
        ),
      );
    } else {
      return _GapoApp(
        routeName: routeName,
        language: language,
        fromNative: fromNative,
      );
    }
  }
}

class _GapoApp extends StatelessWidget {
  final String routeName;
  final String language;
  final bool fromNative;

  const _GapoApp({
    required this.routeName,
    required this.language,
    required this.fromNative,
  });

  Locale get defaultLocal =>
      appLocales
          .firstWhereOrNull((element) => element.languageCode == language) ??
      const Locale("vi");

  double scaleFactor(BuildContext context) {
    // ignore: deprecated_member_use
    if (MediaQuery.of(context).textScaler.textScaleFactor >= _maxScaleFactor) {
      return _maxScaleFactor;
    }

    // ignore: deprecated_member_use
    return MediaQuery.of(context).textScaler.textScaleFactor;
  }

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      theme: AppThemes.theme(),
      darkTheme: AppThemes.darktheme(),
      themeMode: AppThemes().init(),
      locale: defaultLocal,
      fallbackLocale: const Locale("vi"),
      supportedLocales: appLocales,
      // `localizationsDelegates` transfer locale to child widgets, suchas datetime picker
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GPCalendarLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      translationsKeys: AppTranslation.translations,
      initialRoute: fromNative
          ? "${RouterName.loading}?next=$routeName&transaction=${routeName.toInteractionStr()}"
          : routeName,
      debugShowCheckedModeBanner: false,
      getPages: Pages.pages,
      navigatorObservers: [
        _GPNavigatorObserver(),
        SentryNavigatorObserver(enableAutoTransactions: false)
      ],
      routingCallback: (value) {
        if ((value?.isBottomSheet == true) || (value?.isDialog == true)) {
          return;
        }

        ScreenTracking.instance.trackScreen(routeName: value?.current);

        if (value?.isBack == false) {
          if (value != null) {
            Get.find<TagController>().putTag(value.current);
            logDebug("Instance: tags -> ${Get.find<TagController>().tags}");
          }
        }
      },
      // Cập nhật `textScaleFactor` toàn cục
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: TextScaler.linear(scaleFactor(context)),
          ),
          child: child!,
        );
      },
    );
  }
}

class _GPNavigatorObserver extends NavigatorObserver {
  @override
  void didPop(Route route, Route? previousRoute) {
    super.didPop(route, previousRoute);

    if (route.settings.name != null) {
      Get.find<TagController>()
          .removeTag(GPTagUtils().getRoute(route.settings.name!));
    }
  }

  @override
  void didRemove(Route route, Route? previousRoute) {
    super.didRemove(route, previousRoute);

    if (route.settings.name != null) {
      Get.find<TagController>()
          .removeTag(GPTagUtils().getRoute(route.settings.name!));
    }
  }

  @override
  void didPush(Route route, Route? previousRoute) {}

  @override
  void didReplace({Route? newRoute, Route? oldRoute}) {
    // TODO: implement didReplace
    // super.didReplace(newRoute, oldRoute);
  }
}

void _initSentry(SentryFlutterOptions options) {
  if (GetPlatform.isAndroid) {
    options.dsn =
        'https://<EMAIL>/38';
  } else {
    options.dsn =
        'https://<EMAIL>/48';
  }

  options.addIntegration(LoggingIntegration(minEventLevel: Level.INFO));

  options.autoAppStart = false;
  options.enableAutoPerformanceTracing = false;

  // OR if you prefer, determine traces sample rate based on the sampling context
  // options.tracesSampler = (samplingContext) {
  //   return 1;
  //   // return a number between 0 and 1 or null (to fallback to configured value)
  // };

  options.tracesSampleRate = 1.0;
  options.attachStacktrace = true;
  // options.attachViewHierarchy = true;
  options.captureFailedRequests = false;
  options.anrEnabled = true;
  options.enableAppLifecycleBreadcrumbs = true;
  options.enableUserInteractionBreadcrumbs = true;
  options.enableUserInteractionTracing = true;
  options.debug = Constants.isStaging;

  options.profilesSampleRate = 1.0;
  options.reportPackages = false;
  // options.considerInAppFramesByDefault = false;
  options.attachThreads = true;
  options.enableWindowMetricBreadcrumbs = true;
  options.sendDefaultPii = true;
  options.reportSilentFlutterErrors = true;
  options.debug = Constants.isStaging;
  // options.attachScreenshot = true;
  // options.screenshotQuality = SentryScreenshotQuality.low;

  options.maxRequestBodySize = MaxRequestBodySize.always;
  options.maxResponseBodySize = MaxResponseBodySize.always;

  // options.beforeSend = beforeSend;
}

/*
  Sentry tech debt:
  - android device: mở nhiều Flutter engine khi sentry server ngỏm dẫn tới tình trạng ANR
  - 15/01/2023: thêm background isolate, tách riêng phần init sentry ra, nhưng `Sentry.init` có update UI, nên không thể run ở alt isolate.
  `UI actions are only available on root isolate.`
*/
// void _spawnBackgroundIsolate() {
//   RootIsolateToken rootIsolateToken = RootIsolateToken.instance!;
//   Isolate.spawn(_isolateMain, rootIsolateToken);
// }

// Future _isolateMain(RootIsolateToken rootIsolateToken) async {
//   BackgroundIsolateBinaryMessenger.ensureInitialized(rootIsolateToken);

//   await SentryFlutter.init((options) {
//     _initSentry(options);
//   }).catchError((e) {
//     logDebug('SentryFlutter onError: $e');
//   });
// }
// FutureOr<SentryEvent?> beforeSend(SentryEvent event, {Hint? hint}) async {
//   event = event.copyWith(fingerprint: [sentryFingerPrint]);

//   return event;
// }

class GPHttpOverrides extends HttpOverrides {
  @override
  HttpClient createHttpClient(SecurityContext? context) {
    return super.createHttpClient(context)
      ..badCertificateCallback =
          (X509Certificate cert, String host, int port) => true;
  }
}

Set<String> get _environmentFilters {
  final Set<String> result = {};
  if (env == Environment.staging) {
    result.add(kFlavorSaasDevelopment.name);
  } else if (env == Environment.production) {
    result.add(kFlavorSaasProduction.name);
  } else if (env == Environment.momoOnPremise) {
    result.add(kFlavorOnPremiseProduction.name);
  }

  return result;
}
